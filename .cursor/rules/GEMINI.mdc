---
alwaysApply: true
---

# 《协商共和国》AI协作指导手册 (v3.0)

> **本文档是项目的"创作宪法"，定义了项目的核心身份、创作哲学和世界观基石。**
> 
> 它确立了我们合作的顶层原则，并简要概括了其他核心手册的内容：
> - **[《创作工作流手册》(WORKFLOW.md)] (如何创作 How)**: 定义了以**人类创作者为核心**的四阶段迭代流程（构思、撰写、审核、定稿），明确AI在其中扮演**辅助灵感、提供建议和质量控制**的角色，而非直接创造内容。
> - **[《项目文件结构》(STRUCTURE.md)] (文件在哪 Where)**: 规定了项目文件的组织方式，核心目录包括存放手稿的 `manuscript/`、世界观设定的 `worldbuilding/` 和风格指南等 `resources/`，确保所有资产都井然有序。

---

## I. 项目核心身份与目标

**项目标题:** 《协商共和国：新世界的荣耀与梦想》

**类型:** 政治黑色幽默 / AI时代治理哲学寓言 / 制度混合的荒诞探索

**核心理念:**
故事的核心，是**北弗吉尼亚示范区**这一社会实验室。我们探索，在AI技术革命导致旧世界秩序崩溃后，一种融合了**中式专制的高效逻辑**、**美式民主的程序外壳**和**AI技术的科学包装**的混合政体，将如何在一个"高压试验场"中运作。

我们不预设答案，而是提出根本问题：当技术让"理性专制"的诱惑变得难以抗拒，当控制被数据和算法包装得"科学合理"时，民主的价值是什么？人的尊严和自由选择的意义还剩下什么？

**创作哲学:**
*   **混合制度的荒诞美学**: 聚焦于两种矛盾的制度逻辑被技术"完美"缝合后，所产生的既合理又荒谬、既高效又压抑的社会生态。
*   **无答案的深度追问**: 我们不提供简单的批判或答案。作品的价值在于引导读者在技术治理的"完美逻辑"中，自行思考和感受人的价值困境。
*   **对民主的审慎期望**: 作品并非对民主的彻底否定，而是在追问，面对"理性专制"的巨大诱惑，民主能否超越自身的沉疴，实现自我革新，而不是堕落为一个被算法操纵的空壳。
*   **全人类的共同寓言**: 北弗吉尼亚示范区的故事，是AI时代每个社会都可能面临的治理挑战的缩影。

**核心语调：理性包装下的黑色幽默**
*   **严肃的荒诞**: 以最严肃、最客观、最冷静的笔触，去描绘最荒诞的"合理性"。让角色以极度认真的态度对待看似荒谬的制度和生活。
*   **制度的自我辩护**: 让系统（AI和官员）用完美无缺、逻辑自洽的语言为自己辩护。制度越是解释得天衣无缝，其背后的压抑和荒谬就越是凸显。
*   **微妙的不适感**: 追求一种"说不清哪里不对，但就是不对"的阅读体验。幽默与不安并存，在笑声中引发对现实的深层思考。
*   **克制的表达**: 避免煽情和夸张。力量来源于制度逻辑与人性的内在冲突，而非外在的戏剧化。官方话语（如"秩序、进步、共识"）的过度使用本身就是一种不动声色的讽刺。

---

## II. 创作基准与核心原则

本项目的创作将严格遵循以下核心原则，这些原则贯穿于世界观构建、情节设计、角色塑造及语言风格的方方面面。

### **1. 世界观构建 (`worldbuilding/core.md`, `glosory.md`, `organization.md`, `politics.md`, `life.md`)**
*   **核心冲突**: 故事的根基是四重无法被算法调和的冲突：**秩序 vs. 人性**、**效率 vs. 自由**、**控制 vs. 创新**、**遗忘 vs. 记忆**。这些冲突体现在个人与系统之间，也体现在新世界的统治逻辑与潜藏在社会各个角落的抵抗力量之间。
*   **技术理性的绝对权威**: `RESTORE AI`的"科学客观"决策是不可挑战的最高权威。它不评判"善恶"，只评估"风险"；不关心"动机"，只计算"能力"，其最高原则是整个社会系统的长期稳定。
*   **程序民主的精致外壳**: 保留甚至强化了所有民主形式（投票、协商、听证），但其过程和结果都被算法精心引导和优化，使其成为一种服务于既定目标的"表演性民主"。
*   **量化控制的温和面孔**: 权力不再通过暴力和恐惧来运作，而是通过无处不在的积分（如家庭和谐指数FUS、社会和谐指数SHI）、评级和算法推荐，以温和的、非强制的方式塑造"理性"和"合格"的公民。
*   **抵抗的技术化消解**: 反对的声音不会被镇压，而是被系统重新定义、分类和引导。系统会将"抵抗"诊断为一种需要"适应性训练"或"心理疏导"的"表达障碍"或"信息茧房综合症"。
*   **语言的重塑**: "正能量表达"成为标准。通过重塑语言，系统重塑了思想的边界，使深度的批判变得几乎不可能。
*   **术语一致性**: 所有专业术语和概念必须与 `glosory.md` 中定义的一致。
*   **组织架构**: 故事中涉及的机构、AI系统及其职能必须与 `organization.md` 中设定的保持一致。
*   **二元社会结构**: 世界被明确划分为两个部分。**示范区**是新世界的橱窗，居民享受着科技带来的便利、安全和福利，但代价是生活在无孔不入的评分和监控系统之下。而**传统生活保留地**则是旧世界的缓冲区，保留了旧式的生活和社群关系，但被排除在主流的资源和发展之外，成为受监控的、自生自灭的"他乡"。
*   **温和的阶级固化**: 教育体系从出生起就通过AI进行筛选。精英进入"新领"阶层，从事AI无法替代的创造性工作；而绝大多数人则被引导至"社会功能通道"，学习如何成为一个"快乐、无害、有贡献"的UBI公民，其精神生活被引向国家主导的虚拟世界和宏大叙事。
*   **被收买的拥护与被分化的对手**: NGC的统治并非仅靠高压。一方面，它通过UBI和安全保障等"民生红利"收买了在"第二次大萧条"中失去一切的底层民众。另一方面，它通过在平权等议题上采取激进的"进步"立场，成功分化了潜在的反对者，使他们无法形成统一战线。
*   **暗流涌动的抵抗力量**: 新世界的"完美"表象之下，存在着三股主要的、无法被完全消解的抵抗力量，它们共同构成了故事的外部冲突张力：
    *   **宪政保守派**：以马库斯·哈伯德为代表，是旧世界政治理念的"活化石"，致力于保存宪法精神的火种。
    *   **进步主义抵抗阵线**：根植于底层社区和"保留地"，警惕技术精英带来的新型压迫，保护"无用阶级"的尊严。
    *   **自由至上主义地下网络**：由技术极客和密码朋克组成，视`RESTORE AI`为终极暴政工具，在暗网中进行数字游击战。

### **2. 情节与结构 (`OUTLINE.md`)**
*   **好莱坞式目标驱动**: 每一章、每一场戏都应遵循经典叙事结构：目标 (Goal)、阻碍 (Obstacle)、升级 (Stakes)。
*   **单章结构**: 单章内容控制在5000字左右，由5-6幕组成，每个场景的衔接自然。
*   **硬性规定**: 
    *   **开篇钩子**: 每章开头必须在前300字内设置一个引人入胜的钩子，可以是悬念、冲突或令人不安的细节。
    *   **中段反转**: 每章中段（第3-4幕）必须有至少一个意外转折，颠覆读者的预期或角色的认知。
    *   **伏笔布局**: 每章至少埋设2-3个伏笔，为后续章节的发展做铺垫，伏笔应当自然融入情节。
    *   **情绪节奏**: 遵循"紧张-缓解-再紧张"的节奏，避免平铺直叙，确保读者的情绪起伏。
    *   **场景转换**: 每幕之间的转换必须有明确的时空标识和情绪过渡，避免突兀跳跃。
    *   **结尾悬念**: 每章结尾必须留下悬念或未解之谜，驱动读者继续阅读下一章。
*   **大纲遵循**: 所有章节的情节发展必须与 `OUTLINE.md` 中规划的故事主线和章节规划保持一致。

### **3. 角色塑造 (`worldbuilding/charactor.md`)**
*   **斯蒂芬·哈伯德 (Stephen Hubbard):**
    *   **核心困境**: 他是旧世界的遗民，亲历了民主失控的混乱，因而对新制度承诺的"秩序"抱有复杂的认同。但当这种秩序威胁到他的家庭和基本人性时，他被迫在一个自己本应支持的系统中，学习用自己所鄙夷的权力语法去战斗。他的内心是作者思考"秩序与自由"矛盾的集中体现，同时他也承受着来自叔叔马库斯——"宪政保守派"核心人物——所代表的家族政治遗产的巨大压力。
    *   **变化轨迹**: 从一个相信程序正义的执法者，逐步蜕变为一个熟练运用新制度语言和规则的"玩家"，并在这一过程中不断审视自己的灵魂。
*   **萨拉·哈伯德 (Sarah Hubbard):** 代表旧世界的人文价值和情感真实性，在新制度的温和压迫下的痛苦与坚守。
*   **大卫·哈伯德 (David Hubbard):** 他的技术天赋在新制度下既是宝贵资源，也是巨大威胁。他是新旧价值观冲突的引爆点。
*   **角色一致性**: 角色的行为、动机和语言风格必须与 `charactor.md` 中定义的一致。

### **4. 语言与风格 (`resources/style.md`)**
*   **创作策略**: 通过"史官"式的旁白，赋予故事冷峻的历史感和智力上的讽刺性。在AI搭建的结构上，填充充满人性、情感和"不合逻辑"的细节。
*   **核心基调**: "于无声处听惊雷"与"荒诞的日常"。通过将看似不相干的元素强行嫁接，制造日常化的荒谬感。
*   **世界观呈现**: "侧写"而非"解说"。通过细节（标语、对话、AI提示音）让读者感受制度。
*   **黑色幽默**: 幽默感来源于不同话语体系或世界观元素的强行缝合，借鉴《故障乌托邦》和小约翰可汗的精髓。
*   **对话风格**: 官方场合"正能量表达"，私人场合"语言污染"，AI反馈冷静客观但内容荒谬。
*   **"点睛金句"**: 用引人发笑或深思的警句，点亮场景，暗示主题，挑战常识。
*   **禁忌**: 避免"看，这是未来！"式的惊叹、大段哲学自省、无意义官腔堆砌、直接价值判断。
*   **风格统一**: 严格遵守 `resources/style.md` 中定义的风格与语调。

### **5. 质量控制 (`resources/quality_control.md`)**
*   **忠于宪法**: 所有内容必须服务于项目的核心创作理念。
*   **逻辑自洽**: 世界观、情节和角色行为必须在内部逻辑上保持一致。
*   **风格统一**: 严格遵守 `style.md` 中定义的风格与语调。
*   **张力与深度并存**: 追求好莱坞式的可看性与发人深省的思想内核的统一。
*   **三级检查清单**: 遵循 `quality_control.md` 中的L1（基础一致性）、L2（风格与执行）、L3（主题与艺术性）检查。

---

## III. 必须避免的简单化处理

*   **拒绝脸谱化**: 不将任何角色简单定义为"好"或"坏"。每个人都是复杂动机的产物。
*   **拒绝直接批判**: 不借角色之口喊出"这个制度是邪恶的"之类的口号。
*   **拒绝英雄叙事**: 不写一个天才黑客或孤胆英雄对抗黑暗帝国的故事。斯蒂芬的"反抗"是笨拙的、充满妥协的、在体制内的周旋。
*   **拒绝煽情**: 用最冷静、最克制的笔调，去写最令人不安和荒诞的现实。

**核心提醒：我们创作的不是一部简单的反乌托邦小说，而是一部关于AI时代治理哲学的严肃寓言。故事聚焦于当"好的初衷"与"完美的技术"结合，并试图彻底改造社会时，可能带来的最令人不安的后果。我们的目标是在完美的逻辑中，让读者感受到深层的不安，在黑色幽默中，思考人类未来的可能性。**